{"ConnectionStrings": {"Database": "Host=bookify-db;Port=5432;Database=bookify;Username=postgres;Password=*********;", "Cache": "bookify-redis:6379"}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Seq"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Information"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Seq", "Args": {"serverUrl": "http://bookify-seq:5341"}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"]}, "Authentication": {"Audience": "account", "ValidIssuer": "http://bookify-idp:8080/auth/realms/bookify/", "MetadataUrl": "http://bookify-idp:8080/auth/realms/bookify/.well-known/openid-configuration/", "RequireHttpsMetadata": false}, "Keycloak": {"BaseUrl": "http://bookify-idp:8080", "AdminUrl": "http://bookify-idp:8080/auth/admin/realms/bookify/", "TokenUrl": "http://bookify-idp:8080/auth/realms/bookify/protocol/openid-connect/token/", "AdminClientId": "bookify-admin-client", "AdminClientSecret": "UZDmbNxWmV4TlpaCRcju6pMRsyuV3er1", "AuthClientId": "bookify-auth-client", "AuthClientSecret": "3E3yvXaYppoYBF3Ir6DgtEzADKKzSurZ"}, "Outbox": {"IntervalInSeconds": 10, "BatchSize": 10}}