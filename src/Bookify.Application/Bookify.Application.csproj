<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageReference Include="Serilog" Version="4.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Bookify.Domain\Bookify.Domain.csproj" />
  </ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Bookify.Application.UnitTests" />
	</ItemGroup>

</Project>
