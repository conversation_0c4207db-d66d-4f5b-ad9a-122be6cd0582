﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
    <PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
    <PackageReference Include="AspNetCore.HealthChecks.Uris" Version="8.0.1" />
    <PackageReference Include="EFCore.NamingConventions" Version="8.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.6" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="Quartz.Extensions.Hosting" Version="3.9.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Bookify.Application\Bookify.Application.csproj" />
    <ProjectReference Include="..\Bookify.Domain\Bookify.Domain.csproj" />
  </ItemGroup>

    <ItemGroup>
	    <InternalsVisibleTo Include="Bookify.Application.IntegrationTests" />
		<InternalsVisibleTo Include="Bookify.Api.FunctionalTests" />
    </ItemGroup>

</Project>
