﻿using Bookify.Domain.Entities.Users;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Bookify.Infrastructure.Configurations;
internal sealed class UserConfiguration : IEntityTypeConfiguration<User>
{
    public void Configure(EntityTypeBuilder<User> builder)
    {
        builder.ToTable("users");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.Id)
            .HasConversion(userId => userId.Value, value => new UserId(value));

        builder.Property(x => x.FirstName)
            .HasMaxLength(200);

        builder.Property(x => x.LastName)
            .HasMaxLength(200);

        builder.Property(x => x.Email)
            .HasMaxLength(400)
            .HasConversion(email => email.Value, value => new Domain.Entities.Users.ValueObjects.Email(value));

        builder.HasIndex(x => x.Email).IsUnique();

        builder.HasIndex(x => x.IdentityId).IsUnique();
    }
}
