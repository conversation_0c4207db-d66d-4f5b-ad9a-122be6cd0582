{"id": "Bookify", "realm": "bookify", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "9abb6a73-93be-4abc-80be-ae4d75882bab", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "Bookify", "attributes": {}}, {"id": "5210598a-1bd0-459d-91a0-a720e9933ad6", "name": "default-roles-bookify", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["manage-account", "view-profile"]}}, "clientRole": false, "containerId": "Bookify", "attributes": {}}, {"id": "d8f66307-aa71-447f-a21f-a2a317cc1538", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "Bookify", "attributes": {}}], "client": {"realm-management": [{"id": "ee7be1b0-52d0-43c3-97db-70f7eee91ebe", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "daf110b6-1fc0-4aa0-b38a-8e5d1fa12c2f", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "f14c9564-6276-4e7a-8251-884f129a8aea", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "2655e7ad-7da8-4ef8-9eca-a898c7ca571d", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "3940d4c8-1aec-4312-87e5-5e781df29e2d", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "a52a8293-878b-478e-ac14-39c7def59aaa", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "bbfb2894-56c0-49bb-b800-518fc344b36f", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "65ad1dbd-e872-4b74-a345-81c86d9c135d", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "25077c66-a6df-48c1-b612-d742ee52c904", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "e3f71de1-c732-4fe6-9e3f-487d66b13a0d", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "69cc1fd8-778b-46c5-9a8c-0b029485e210", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "72a25d29-b707-47e0-a719-1818b023aa49", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-groups", "query-users"]}}, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "b88a40a8-558a-418c-a80c-e035ee6bf03e", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "de809617-c283-4036-8e11-3c2431018384", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "b69542ba-d376-421e-9464-9f0a0c9d58c4", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "cf353da6-128b-43a5-a4ec-53b4006dea1f", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "b1bcc217-bbde-47da-ac21-50c494c8dff5", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-events", "impersonation", "query-groups", "view-realm", "query-realms", "manage-realm", "view-authorization", "manage-authorization", "manage-clients", "manage-identity-providers", "create-client", "view-users", "query-clients", "view-clients", "view-identity-providers", "query-users", "manage-users", "view-events"]}}, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "1a67a641-fae3-4a01-b4e8-c331976ff67e", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}, {"id": "00b324f6-ccb8-424a-99c7-79f2852f1bbe", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "c67cc095-3c66-481e-a511-4b57e64932d7", "attributes": {}}], "security-admin-console": [], "bookify-auth-client": [], "admin-cli": [], "bookify-admin-client": [], "account-console": [], "broker": [{"id": "3e4d0113-0d33-4c3c-9097-4c7be0d88382", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "4bbd22e5-1ca4-46e7-b5b5-067ca4ef355a", "attributes": {}}], "account": [{"id": "bfb9c9b8-1e57-4d3c-ae89-d8683dea5b5d", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "9da81312-1b2b-4503-98f8-5b4afb15c85a", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "f117f401-5caa-4912-be9c-8ef3c5e92c50", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "e65aa19e-2465-4d40-8fc4-5b3f7aa95066", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "5a24eb45-6f11-4975-bab5-99d3d38b9681", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "73d3bf6b-f5d3-4bb7-9296-a6152686231e", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}, {"id": "39b1f859-e505-4c10-918a-06b0e5d334ec", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "773bd271-88de-43d4-b68e-325af3050cdf", "attributes": {}}]}}, "groups": [], "defaultRole": {"id": "5210598a-1bd0-459d-91a0-a720e9933ad6", "name": "default-roles-bookify", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "Bookify"}, "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpSupportedApplications": ["FreeOTP", "Google Authenticator"], "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "users": [{"id": "3538994f-b095-45f6-8f98-a95dd61b84c1", "createdTimestamp": *************, "username": "service-account-bookify-admin-client", "enabled": true, "totp": false, "emailVerified": false, "serviceAccountClientId": "bookify-admin-client", "disableableCredentialTypes": [], "requiredActions": [], "realmRoles": ["default-roles-bookify"], "clientRoles": {"realm-management": ["manage-users"]}, "notBefore": 0, "groups": []}], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account"]}]}, "clients": [{"id": "773bd271-88de-43d4-b68e-325af3050cdf", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/bookify/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/bookify/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "693fe220-c7bf-4c02-a850-291206b252da", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/Bookify/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/Bookify/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "5e809bf9-3c5b-4362-b654-ee864bba3f46", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "6c606d01-09b5-45ad-837e-f3a7b8d58cf7", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c6bfa492-b859-47b2-96e0-18b0ad6c9b6d", "clientId": "bookify-admin-client", "rootUrl": "http://localhost:18080/", "adminUrl": "http://localhost:18080/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "UZDmbNxWmV4TlpaCRcju6pMRsyuV3er1", "redirectUris": ["http://localhost:18080/*"], "webOrigins": ["http://localhost:18080"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": true, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "access.token.lifespan": "600", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "require.pushed.authorization.requests": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {"direct_grant": "096d742b-9e85-42cc-bcac-76d49316fbf2"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "4ea4883c-c3d3-48da-a3a3-cd10b7ab7f95", "name": "Client ID", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientId", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientId", "jsonType.label": "String"}}, {"id": "4ab7b15b-02df-46e6-a670-bf33a831c54b", "name": "Client IP Address", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientAddress", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientAddress", "jsonType.label": "String"}}, {"id": "cd44d58f-0114-445f-9bae-dbffc9be7483", "name": "Client Host", "protocol": "openid-connect", "protocolMapper": "oidc-usersessionmodel-note-mapper", "consentRequired": false, "config": {"user.session.note": "clientHost", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "clientHost", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "********-c4e0-4354-9de4-3194af25fd9d", "clientId": "bookify-auth-client", "rootUrl": "http://localhost:18080/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "secret": "3E3yvXaYppoYBF3Ir6DgtEzADKKzSurZ", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "access.token.lifespan": "3600", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "require.pushed.authorization.requests": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {"direct_grant": "096d742b-9e85-42cc-bcac-76d49316fbf2", "browser": "8bc20504-4740-4115-93a6-ee7b403391f6"}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "4bbd22e5-1ca4-46e7-b5b5-067ca4ef355a", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "c67cc095-3c66-481e-a511-4b57e64932d7", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "5966c53d-17eb-4ecf-8f70-5899f92c7636", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/bookify/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/bookify/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "e9fa6e8c-239b-462d-bb1a-4d9d4c8d6a28", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "a6e3c9da-99c1-4693-83da-d3fe3d2fdc04", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "f8c214e7-7fe1-4519-baff-d2c265018bbc", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "c01702da-bc07-46e1-b909-4a86ff5c78f1", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "1d55578f-498c-4206-9bfa-3ed09704aa6a", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "6ce20fd9-6537-48f8-b56a-8d5cfd2f4da1", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}, {"id": "0d165208-2709-409d-9031-207060acfe58", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}]}, {"id": "4a60e42c-7120-4065-baee-0404acb7a7c4", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "a364eac7-b9b1-45cf-bdd3-9147440d05a0", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "f0e3170d-457d-4c90-b700-df5a11c05aee", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "89577813-ae97-4cbb-beee-f6f40053e170", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "6490192b-81d4-4f58-9b4c-7572223f7d1e", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "212c5804-741e-4d51-9bf0-e9b429587a1f", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "f1b21fc7-6d65-4bbc-93b1-c24f9ce28c0d", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}]}, {"id": "853bd042-3eec-473a-8ac2-4dac73629027", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "2c797b57-757b-4052-ae05-7c0904badc2b", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "2723907c-8f05-4737-964c-66d33a6c1b30", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "2ab0164b-55b0-42ff-b3ea-cdc7e384cf55", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}, {"id": "2275aaff-7f9f-4421-8366-1f30d699070a", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}]}, {"id": "2617f2ce-1f77-417c-bab7-fed0642b61d4", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "2f164f03-fb37-43a2-84f0-05546ea38f64", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "3370eec6-af1b-4a41-ad1e-ed37a11e3377", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "555fe628-7601-498a-a915-327980ff7e7c", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "3bcc5486-50f0-4a0a-8f7e-fe57988912b8", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "0622976c-257e-4c62-a372-f3375d4f759a", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "b6cd974f-77b3-4651-8c58-c711d35f2338", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}, {"id": "bd07e698-435d-4095-ad44-ce56178c3d0a", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "c0516bfc-0cf3-4606-88d2-b4fab17c9e8e", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "d6c28f1f-c20c-47db-9bf2-accb9899ace9", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "b745e89a-7fdf-41d5-8799-82fbe7dd76b6", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "f95e6c60-1e2f-4250-a42a-9bdd6c535e9f", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "495d4dcf-d809-41f7-8adc-f7fc0116ed0e", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "04097b0e-0b57-4348-aa13-73ca2542fd6f", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "86e201bb-b70b-438b-a08f-75d904674d52", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}]}, {"id": "69797203-dba7-4f5f-bfd1-8f214857e2ea", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "f2582a2a-fe13-41a4-9264-25484d918e51", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "e4199f80-cb97-4f42-b622-ed1cef6fab1b", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "ac600cf8-b781-4c9a-bdc4-fbed18b830a0", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-attribute-mapper", "saml-role-list-mapper", "oidc-usermodel-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-property-mapper", "oidc-address-mapper", "saml-user-property-mapper", "oidc-sha256-pairwise-sub-mapper"]}}, {"id": "c04eb674-b0ef-4703-8894-dfd70439656f", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "edd4c8d4-215d-4120-8db8-251534ffe5ab", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "64402729-7b44-4175-8817-a78bfd6af168", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "f8177340-6b80-47a6-bc1b-8f4a04d96e5d", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["saml-user-property-mapper", "saml-role-list-mapper", "oidc-address-mapper", "saml-user-attribute-mapper", "oidc-sha256-pairwise-sub-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-full-name-mapper"]}}, {"id": "25a2fce9-1c38-45c9-be15-75d01a72283c", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "228613e8-2ec8-428b-a58f-94b92cf7fe49", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "eb5fced0-353c-4195-aa79-73a625ae13e8", "providerId": "declarative-user-profile", "subComponents": {}, "config": {}}], "org.keycloak.keys.KeyProvider": [{"id": "1e958ad2-5ad5-4b4d-b586-97d5bb37d725", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "d3883d39-1321-496c-a54a-cadcc3c7c59c", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "8c82fe74-72b6-4448-9ce2-103b496768b2", "name": "rsa-enc-generated", "providerId": "rsa-enc-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["RSA-OAEP"]}}, {"id": "eb9f0587-66c7-49a0-a3a1-04d2a5bbcc74", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "3788a4eb-9d77-495a-b8b5-fac69d07a4c1", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "02b8a613-32a6-46bd-a330-e278ddecaf7a", "alias": "Authentication Options", "description": "Authentication options.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "basic-auth", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "basic-auth-otp", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c186a253-6710-4431-9201-690248237d50", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "84033a16-0091-43be-818a-d8f3486fd325", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "c83bb832-45b4-41db-a61c-88ae26e0b778", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "d2e8d6ed-e69e-405f-8636-d7b6061f57fc", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "Account verification options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "********-581f-414b-8c5e-8663bfa8451c", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "abe06f6d-303a-4050-a552-3cec54bd339e", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "8ae7e04b-9c8c-4057-a394-78ebb3da0c72", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "8bc20504-4740-4115-93a6-ee7b403391f6", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "flowAlias": "forms", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "15c1fc19-5ecb-410f-a074-6517945f650a", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "096d742b-9e85-42cc-bcac-76d49316fbf2", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "892aeb02-bf02-49eb-a6f2-71f045dfe2b6", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "e9eba6bb-b370-4871-81c9-088a207cf231", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "User creation or linking", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "fa7e2309-6240-45ec-aa74-d146696182cc", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "38df2853-0a89-4041-bfab-40e13ee2cc97", "alias": "http challenge", "description": "An authentication flow based on challenge-response HTTP Authentication Schemes", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "no-cookie-redirect", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "flowAlias": "Authentication Options", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "2c7dc3b8-f22a-46fc-9fe9-277fb15aab26", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "flowAlias": "registration form", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f0677bbf-7160-468b-a62a-04e9834ae431", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-profile-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 40, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "userSetupAllowed": false, "autheticatorFlow": false}]}, {"id": "b377da26-bda6-4b6a-88aa-2b27981e8cfa", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "userSetupAllowed": false, "autheticatorFlow": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false, "autheticatorFlow": true}]}, {"id": "f475168e-2802-4ca2-af99-1681c64b7a9b", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "userSetupAllowed": false, "autheticatorFlow": false}]}], "authenticatorConfig": [{"id": "ee26a5e6-6660-4083-8f15-9ac2f4daaee8", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "c6ad07a6-1e97-498a-a0ed-4dfa0628539b", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "terms_and_conditions", "name": "Terms and Conditions", "providerId": "terms_and_conditions", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "oauth2DevicePollingInterval": "5", "clientOfflineSessionMaxLifespan": "0", "clientSessionIdleTimeout": "0", "userProfileEnabled": "false", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5"}, "keycloakVersion": "16.1.1", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}