# This workflow will build a .NET project
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-net

name: .NET

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3
    - name: Setup .NET
      uses: actions/setup-dotnet@v3
      with:
        dotnet-version: 8.0.x
    
    - name: Restore dependencies
      run: dotnet restore
      working-directory: src/Bookify.API
    
    - name: Build
      run: dotnet build --no-restore --configuration Release
      working-directory: src/Bookify.API

    - name: Test Application
      run: dotnet test 
      working-directory: test/Bookify.Application.UnitTests
       
    - name: Test Domain
      run: dotnet test 
      working-directory: test/Bookify.Domain.UnitTests
        
    - name: Test Architecture
      run: dotnet test 
      working-directory: test/Bookify.ArchitectureTests    
