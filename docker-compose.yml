version: '3.4'

services:
  bookify.api:
    image: ${DOCKER_REGISTRY-}bookifyapi
    container_name: Bookify.API
    build:
      context: .
      dockerfile: src/Bookify.API/Dockerfile
    depends_on:
      - bookify-db
  
  bookify-db:
    image: postgres:latest
    container_name: Bookify.Db
    environment:
      - POSTGRES_DB=bookify
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=Abcd1234!
    volumes:
      - ./.container/database:/var/lib/postgresql/data
    ports:
      - 5432:5432

  bookify-idp:
    image: jboss/keycloak:latest
    container_name: Bookify.Identity
    environment:
      - KEYCLOAK_USER=admin
      - KEYCLOAK_PASSWORD=admin
      - KEYCLOAK_IMPORT=/tmp/bookify-realm-export.json
    volumes:
      - ./.containers/identity:/opt/jboss/keycloak/standalone/data
      - ./.files/bookify-realm-export.json:/tmp/bookify-realm-export.json
    ports:
      - 18080:8080

  bookify-seq:
    image: datalust/seq:latest
    container_name: Bookify.Seq
    environment:
     - ACCEPT_EULA=Y
    ports:
     - 5341:5341
     - 8081:80

  bookify-redis:
    image: redis:latest
    container_name: Bookify.Redis
    restart: always
    ports:
     - 6379:6379